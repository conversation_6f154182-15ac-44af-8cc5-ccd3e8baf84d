import logging
import numpy as np
import pandas as pd
import os
import re
from datetime import datetime
from typing import Dict, Any, Optional
from stable_baselines3 import PPO
from .base_strategy import BaseStrategy
from app.utils.indicator_calculator import IndicatorCalculator
from app.trade_manager.portfolio_manager import PortfolioManager
from app.trade_manager.trade_manager import TradeManager
from .trading_env import TradingEnv

logger = logging.getLogger(__name__)


class PPOStrategy(BaseStrategy):
    """RL-based strategy using Proximal Policy Optimization (PPO)"""

    def __init__(self, pair: str,
                 train_on_startup: bool = False,  # Whether to train on initialization
                 short_window: int = 7,
                 long_window: int = 21,
                 take_profit_levels: list = [0.05, 0.1, 0.15],
                 stop_loss: float = -0.2,
                 interval: str = '1h',
                 max_investment: float = 7500,
                 taker_maker_fee: float = 0.0026,
                 min_investment: float = 250,
                 position_size_multiplier: float = 20.0,
                 volatility_multiplier: float = 3.0):

        super().__init__(
            pair=pair,
            take_profit_levels=take_profit_levels,
            stop_loss=stop_loss,
            interval=interval,
            max_investment=max_investment,
            taker_maker_fee=taker_maker_fee,
            min_investment=min_investment,
            position_size_multiplier=position_size_multiplier,
            volatility_multiplier=volatility_multiplier
        )

        self.short_window = short_window
        self.long_window = long_window
        self.model_path = self.find_newest_model()
        self.train_on_startup = train_on_startup
        self.model = None
        self.env = None
        self.portfolio_manager = None
        self.trade_manager = None
        self.historical_data = pd.DataFrame()
        self.last_action = 0  # 0 = hold, 1 = buy, 2 = sell
        self.retrain_interval_days = 7  # Days between retraining
        # Last time the model was trained
        self.last_training_time: Optional[datetime] = None

        # Initialize model if path is provided
        if self.model_path:
            try:
                self.model = PPO.load(self.model_path)
                logger.info(f"Loaded PPO model from {self.model_path}")
            except Exception as e:
                logger.error(f"Failed to load model: {e}")

    def find_newest_model(self, models_folder="models"):
        pattern = re.compile(r"ppo_SOLUSD_(\d{8}_\d{6})\.zip")
        newest_model = None
        newest_timestamp = None

        for filename in os.listdir(models_folder):
            match = pattern.match(filename)
            if match:
                timestamp_str = match.group(1)
                timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                if newest_timestamp is None or timestamp > newest_timestamp:
                    newest_timestamp = timestamp
                    newest_model = filename

        if newest_model:
            return os.path.join(models_folder, newest_model)
        else:
            return None  # No matching models found

    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepares data before running the strategy"""
        logger.info("Preprocessing data for PPO strategy")
        cols_to_convert = ['open_price', 'high_price',
                           'low_price', 'close_price', 'volume']
        df[cols_to_convert] = df[cols_to_convert].apply(
            pd.to_numeric, errors='coerce')
        # df = self.resample_data(df)

        # --- Calculate indicators ---
        # Trend
        df["EMA_short"] = IndicatorCalculator.calculate_ema(df, 7)
        df["EMA_long"] = IndicatorCalculator.calculate_ema(df, 21)

        # New Trend Indicators
        df["macd"], df["macd_signal"], df["macd_hist"] = IndicatorCalculator.calculate_macd(
            df)
        df["sar"] = IndicatorCalculator.calculate_parabolic_sar(df)

        # Momentum
        df["rsi"] = IndicatorCalculator.calculate_rsi(df)
        df["momentum"] = IndicatorCalculator.calculate_momentum(df, 10)
        df["stoch_k"], df["stoch_d"] = IndicatorCalculator.calculate_stochastic(
            df)
        df["cci"] = IndicatorCalculator.calculate_commodity_channel_index(df)
        df["roc"] = IndicatorCalculator.calculate_rate_of_change(df)

        # Volatility
        df["atr"] = IndicatorCalculator.calculate_atr(df)
        df["adx"] = IndicatorCalculator.calculate_average_directional_index(df)
        df["bb_upper"], df["bb_middle"], df["bb_lower"] = IndicatorCalculator.calculate_bollinger_bands(
            df)
        df["bb_percent_b"] = IndicatorCalculator.calculate_bollinger_percent_b(
            df)
        df["volatility_ratio"] = IndicatorCalculator.calculate_volatility_ratio(
            df)

        # Volume
        df["vwap"] = IndicatorCalculator.calculate_vwap(df)
        df["obv"] = IndicatorCalculator.calculate_on_balance_volume(df)
        df["relative_volume"] = IndicatorCalculator.calculate_relative_volume(
            df)
        df["volume_roc"] = IndicatorCalculator.calculate_volume_roc(df)
        df["stddev_returns"] = IndicatorCalculator.calculate_stddev_of_returns(
            df)
        df["mfi"] = IndicatorCalculator.calculate_money_flow_index(df)
        df["ad_line"] = IndicatorCalculator.calculate_chaikin_ad_line(df)
        df["adosc"] = IndicatorCalculator.calculate_chaikin_oscillator(df)
        df['vwap_slope'] = IndicatorCalculator.calculate_vwap_slope(df)
        df['obv_slope'] = IndicatorCalculator.calculate_obv_slope(df)

        # Volatility Bands
        df["kc_upper"], df["kc_middle"], df["kc_lower"] = IndicatorCalculator.calculate_keltner_channels(
            df)

        # Market Regime
        df["market_regime"] = IndicatorCalculator.detect_market_regime(df)

        # Candlestick Patterns
        df = IndicatorCalculator.detect_candlestick_patterns(df)

        # Temporal alignment for order book features: apply exponential weighted average with 1 hour span
        ob_feature_names = [
            'ob_spread', 'ob_spread_pct', 'ob_mid_price', 'ob_volume_imbalance',
            'ob_bid_depth_5', 'ob_ask_depth_5', 'ob_total_bid_vol', 'ob_total_ask_vol',
            'ob_bid_impact', 'ob_ask_impact', 'ob_weighted_bid', 'ob_weighted_ask'
        ]
        for feature_name in ob_feature_names:
            if feature_name in df.columns:
                # Apply exponential weighted average with 1 hour span (assuming data frequency is minutes)
                df[feature_name] = df[feature_name].ewm(span=60, adjust=False).mean()

        # Normalize order book features if present
        for feature_name in ob_feature_names:
            if feature_name in df.columns:
                # Use robust normalization for volume-related features, zscore for others
                if feature_name in ['ob_bid_depth_5', 'ob_ask_depth_5', 'ob_total_bid_vol', 'ob_total_ask_vol']:
                    df[f'{feature_name}_norm'] = self._normalize_feature(
                        df[feature_name], method='robust', window=48*60, clip=5.0)  # 48 hours window assuming 1-min freq
                else:
                    df[f'{feature_name}_norm'] = self._normalize_feature(
                        df[feature_name], method='zscore', window=48*60, clip=5.0)

        # Add order book momentum features (momentum of ob_spread and ob_volume_imbalance)
        if 'ob_spread' in df.columns:
            df['ob_spread_momentum'] = df['ob_spread'].diff(1)
            df['ob_spread_momentum_norm'] = self._normalize_feature(
                df['ob_spread_momentum'], method='zscore', window=48*60, clip=5.0)
        if 'ob_volume_imbalance' in df.columns:
            df['ob_volume_imbalance_momentum'] = df['ob_volume_imbalance'].diff(1)
            df['ob_volume_imbalance_momentum_norm'] = self._normalize_feature(
                df['ob_volume_imbalance_momentum'], method='zscore', window=48*60, clip=5.0)

        df = df.dropna()
        # Store processed data
        self.historical_data = df.copy()

        # Train the model if specified and sufficient data
        if self.train_on_startup and len(df) > 1000 and self.model is None:
            self._train_model(df)

        return df

    def _default_order_book_features(self):
        return {
            'ob_spread': 0.0,
            'ob_spread_pct': 0.0,
            'ob_mid_price': 0.0,
            'ob_volume_imbalance': 0.0,
            'ob_bid_depth_5': 0.0,
            'ob_ask_depth_5': 0.0,
            'ob_total_bid_vol': 0.0,
            'ob_total_ask_vol': 0.0,
            'ob_bid_impact': 0.0,
            'ob_ask_impact': 0.0,
            'ob_weighted_bid': 0.0,
            'ob_weighted_ask': 0.0
        }

    def calculate_orderbook_features(self, orderbook_data: dict) -> dict:
        """
        Calculate order book features from orderbook_data dict.

        orderbook_data format:
        {
            'pair': str,
            'snapshot_time': datetime,
            'bids': [{'price': float, 'qty': float}, ...],
            'asks': [{'price': float, 'qty': float}, ...],
            'checksum': int
        }
        """
        bids = orderbook_data.get('bids', [])
        asks = orderbook_data.get('asks', [])

        # Defensive: if bids or asks are empty, return defaults
        if not bids or not asks:
            return self._default_order_book_features()

        # Validate and clean bid/ask data
        try:
            # Filter out invalid entries and convert to float
            valid_bids = []
            for bid in bids:
                if isinstance(bid, dict) and 'price' in bid and 'qty' in bid:
                    price = float(bid['price'])
                    qty = float(bid['qty'])
                    if price > 0 and qty > 0:  # Only include positive prices and quantities
                        valid_bids.append({'price': price, 'qty': qty})

            valid_asks = []
            for ask in asks:
                if isinstance(ask, dict) and 'price' in ask and 'qty' in ask:
                    price = float(ask['price'])
                    qty = float(ask['qty'])
                    if price > 0 and qty > 0:  # Only include positive prices and quantities
                        valid_asks.append({'price': price, 'qty': qty})

            # Check if we have valid data after filtering
            if not valid_bids or not valid_asks:
                return self._default_order_book_features()

            # Sort bids descending by price, asks ascending by price
            bids_sorted = sorted(valid_bids, key=lambda x: x['price'], reverse=True)
            asks_sorted = sorted(valid_asks, key=lambda x: x['price'])

            best_bid = bids_sorted[0]['price']
            best_ask = asks_sorted[0]['price']

            # Validate market integrity - spread should be non-negative
            if best_ask < best_bid:
                # Crossed market detected - this shouldn't happen in normal conditions
                # Log warning and return defaults
                logger.warning(f"Crossed market detected: best_bid={best_bid}, best_ask={best_ask}")
                logger.warning(f"Sample bids: {bids_sorted[:3]}")
                logger.warning(f"Sample asks: {asks_sorted[:3]}")
                return self._default_order_book_features()

            ob_spread = best_ask - best_bid
            ob_spread_pct = (ob_spread / best_bid * 100) if best_bid > 0 else 0.0
            ob_mid_price = (best_ask + best_bid) / 2

            # Additional validation for debugging
            if ob_spread < 0:
                logger.error(f"Negative spread calculated: {ob_spread}, best_bid={best_bid}, best_ask={best_ask}")
                return self._default_order_book_features()

        except (ValueError, TypeError, KeyError) as e:
            logger.warning(f"Error processing order book data: {e}")
            return self._default_order_book_features()

        try:
            bid_volumes = [level['qty'] for level in bids_sorted]
            ask_volumes = [level['qty'] for level in asks_sorted]

            total_bid_vol = sum(bid_volumes)
            total_ask_vol = sum(ask_volumes)

            ob_volume_imbalance = (total_bid_vol - total_ask_vol) / \
                (total_bid_vol + total_ask_vol) if (total_bid_vol + total_ask_vol) > 0 else 0.0

            ob_bid_depth_5 = sum(bid_volumes[:5])
            ob_ask_depth_5 = sum(ask_volumes[:5])

            # VWAP calculations for top 5 bids and asks
            def vwap(levels):
                if not levels:
                    return 0.0
                total_vol = sum(level['qty'] for level in levels)
                if total_vol == 0:
                    return 0.0
                return sum(level['price'] * level['qty'] for level in levels) / total_vol

            ob_weighted_bid = vwap(bids_sorted[:5])
            ob_weighted_ask = vwap(asks_sorted[:5])

            # Price impact calculations for buying/selling 1000 units
            def price_impact(levels, qty_to_trade):
                if not levels or qty_to_trade <= 0:
                    return 0.0
                remaining_qty = qty_to_trade
                impact_price = 0.0
                for level in levels:
                    level_qty = level['qty']
                    trade_qty = min(remaining_qty, level_qty)
                    impact_price += trade_qty * level['price']
                    remaining_qty -= trade_qty
                    if remaining_qty <= 0:
                        break
                return (impact_price / qty_to_trade) - levels[0]['price']

            ob_bid_impact = price_impact(bids_sorted, 1000)
            ob_ask_impact = price_impact(asks_sorted, 1000)

            return {
                'ob_spread': ob_spread,
                'ob_spread_pct': ob_spread_pct,
                'ob_mid_price': ob_mid_price,
                'ob_volume_imbalance': ob_volume_imbalance,
                'ob_bid_depth_5': ob_bid_depth_5,
                'ob_ask_depth_5': ob_ask_depth_5,
                'ob_total_bid_vol': total_bid_vol,
                'ob_total_ask_vol': total_ask_vol,
                'ob_bid_impact': ob_bid_impact,
                'ob_ask_impact': ob_ask_impact,
                'ob_weighted_bid': ob_weighted_bid,
                'ob_weighted_ask': ob_weighted_ask
            }

        except Exception as e:
            logger.warning(f"Error calculating order book features: {e}")
            return self._default_order_book_features()

    def _normalize_feature(self, series: pd.Series, method: str = 'zscore', window: int = 20, clip: float = 5.0) -> pd.Series:
        """
        Normalize a feature for PPO-based RL agent.

        Parameters:
        - series: the feature to normalize
        - method: 'zscore', 'minmax', or 'robust'
        - window: rolling window size
        - clip: max absolute value to clip normalized output

        Returns:
        - Normalized and clipped pd.Series
        """
        if method == 'zscore':
            rolling_mean = series.rolling(window=window).mean()
            rolling_std = series.rolling(window=window).std()

            normalized = (series - rolling_mean) / rolling_std
        elif method == 'minmax':
            rolling_min = series.rolling(window=window).min()
            rolling_max = series.rolling(window=window).max()

            normalized = (series - rolling_min) / \
                (rolling_max - rolling_min + 1e-8)
        elif method == 'robust':
            rolling_median = series.rolling(window=window).median()
            rolling_mad = series.rolling(window=window).apply(lambda x: x.mad(), raw=False)

            normalized = (series - rolling_median) / (rolling_mad + 1e-8)
        else:
            raise ValueError(f"Unsupported normalization method: {method}")

        # Fill NaNs and clip outliers
        normalized = normalized.fillna(0.0)
        normalized = normalized.clip(lower=-clip, upper=clip)

        return normalized

    def linear_schedule(self, initial_value, final_value):
        """Returns a function that computes a linear schedule."""
        def schedule(progress_remaining):
            return initial_value + (final_value - initial_value) * (1 - progress_remaining)
        return schedule

    def _train_model(self, df: pd.DataFrame, timesteps: int = 100000):
        """Train the PPO model on historical data"""
        logger.info(f"Training PPO model on {len(df)} data points")

        # Create and configure the environment
        train_env = TradingEnv(df, PPOStrategy(self.pair))
        train_env.render_mode = "human"

        # Create and train the model
        model = PPO(
            "MlpPolicy",
            train_env,
            verbose=1,
            learning_rate=self.linear_schedule(0.0003, 0.00005),
            n_steps=2048,
            batch_size=64,
            n_epochs=10,
            gamma=0.99,
            gae_lambda=0.95,
            clip_range=0.2,
            ent_coef=0.01,
            vf_coef=0.5,
            max_grad_norm=0.5,
            tensorboard_log=f"./tensorboard_logs/{self.pair}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )

        # Train the model
        try:
            model.learn(total_timesteps=timesteps)
            # Save the trained model
            model_save_path = f"./models/ppo_{self.pair}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            model.save(model_save_path)
            logger.info(f"Model saved to {model_save_path}")
            self.model = model
            self.model_path = model_save_path
        except Exception as e:
            logger.error(f"Error training model: {e}")

    def reset_historical_data(self, historical_data: Optional[pd.DataFrame] = None):
        """
        Reset the historical data to the provided DataFrame or an empty DataFrame if None.
        If the provided historical_data has more than 1000 rows, only keep the most recent 1000.

        Args:
            historical_data (pd.DataFrame, optional): The DataFrame to set as historical data. Defaults to None.
        """
        if historical_data is not None and not historical_data.empty:
            # Keep only the most recent 1000 rows if historical_data is larger
            self.historical_data = historical_data.tail(1000).copy() if len(
                historical_data) > 1000 else historical_data.copy()
        else:
            self.historical_data = pd.DataFrame()

        logger.info(
            f"Historical data reset with {len(self.historical_data)} candles")

    def process_candle(self, candle: pd.Series, previous_candle: pd.Series, minusthree_candle: pd.Series, trade_manager: TradeManager, portfolio_manager: PortfolioManager):
        # Update current step and tracking variables
        self.current_price = candle['close_price']
        self.trade_manager = trade_manager
        self.portfolio_manager = portfolio_manager

        # Process the candle with the base strategy
        super().process_candle(candle, previous_candle,
                               minusthree_candle, trade_manager, portfolio_manager)

        # Add the new candle to historical data
        new_data = pd.DataFrame([candle])
        self.historical_data = pd.concat([self.historical_data, new_data])

        # Limit historical data to 1000 most recent candles to prevent memory issues
        if len(self.historical_data) > 1000:
            logger.debug(
                f"Limiting historical data from {len(self.historical_data)} to 1000 most recent candles")
            self.historical_data = self.historical_data.iloc[-1000:].reset_index(
                drop=True)

    def should_enter_trade(self, candle: pd.Series, previous_candle: pd.Series, minusthree_candle: pd.Series) -> bool:
        """
        Determine if a trade should be entered based on the model's prediction.
        previous_candle and minusthree_candle parameters are required by interface but not used
        """
        # Suppress unused parameter warnings
        _ = (previous_candle, minusthree_candle)
        if self.model is None:
            return False
        observation = self._prepare_observation(candle)
        action, _ = self.model.predict(observation, deterministic=True)
        base_decision = action == 1

        # Apply dynamic order book filters based on market regime and time of day
        ob_spread_pct = candle.get('ob_spread_pct', 0.0)
        ob_bid_depth_5 = candle.get('ob_bid_depth_5', 0.0)
        ob_volume_imbalance = candle.get('ob_volume_imbalance', 0.0)
        market_regime = candle.get('market_regime', 'neutral')
        hour = candle['timestamp'].hour if 'timestamp' in candle else None

        # Dynamic thresholds example
        spread_threshold = 0.5 if market_regime == 'neutral' else 0.7
        bid_depth_threshold = 100 if market_regime != 'volatile' else 150
        volume_imbalance_threshold = -0.3 if market_regime == 'neutral' else -0.2

        # Time of day filter: avoid trading during low liquidity hours (e.g., 0-6 UTC)
        if hour is not None and (hour >= 0 and hour < 6):
            return False

        if ob_spread_pct > spread_threshold:
            return False
        if ob_bid_depth_5 < bid_depth_threshold:
            return False
        if ob_volume_imbalance < volume_imbalance_threshold:
            return False

        return base_decision

    def should_exit_trade(self, candle: pd.Series, previous_candle: pd.Series) -> bool:
        """
        Determine if a trade should be exited based on the model's prediction.
        previous_candle parameter is required by interface but not used
        """
        # Suppress unused parameter warning
        _ = previous_candle
        if self.model is None:
            return False
        observation = self._prepare_observation(candle)
        action, _ = self.model.predict(observation, deterministic=True)
        base_decision = action == 2

        # Apply dynamic order book filters based on market regime and time of day
        ob_total_bid_vol = candle.get('ob_total_bid_vol', 0.0)
        ob_total_ask_vol = candle.get('ob_total_ask_vol', 0.0)
        ob_spread_pct = candle.get('ob_spread_pct', 0.0)
        market_regime = candle.get('market_regime', 'neutral')
        hour = candle['timestamp'].hour if 'timestamp' in candle else None

        # Dynamic thresholds example
        volume_threshold = 50 if market_regime == 'neutral' else 75
        spread_exit_threshold = 1.0 if market_regime == 'neutral' else 1.2

        # Time of day filter: allow exit anytime, no restriction

        if (ob_total_bid_vol + ob_total_ask_vol) < volume_threshold:
            return True
        if ob_spread_pct > spread_exit_threshold:
            return True

        return base_decision

    def _prepare_observation(self, candle: pd.Series) -> np.ndarray:
        """Use TradingEnv's logic to build the observation"""
        # Create a one-row DataFrame from the candle
        df = pd.DataFrame([candle])
        env = TradingEnv(df, portfolio_manager=self.portfolio_manager,
                         trade_manager=self.trade_manager)
        # Force current_step to 0 (only one row)
        env.current_step = 0
        obs = env._next_observation()
        if obs is None:
            obs = np.zeros(env._calculate_num_features(), dtype=np.float32)
        return obs.reshape(1, -1)  # Required shape for model.predict

    def save_model(self, path: Optional[str] = None) -> Optional[str]:
        """Save the current model to disk"""
        if self.model is None:
            logger.error("No model to save")
            return None

        if path is None:
            path = f"./models/ppo_{self.pair}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"

        try:
            self.model.save(path)
            logger.info(f"Model saved to {path}")
            return path
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            return None

    def load_model(self, path: str) -> bool:
        """Load a model from disk"""
        try:
            self.model = PPO.load(path)
            self.model_path = path
            logger.info(f"Loaded PPO model from {path}")
            return True
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            return False
