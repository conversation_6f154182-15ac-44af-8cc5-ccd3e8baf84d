from app.strategy.ppo_strategy import PPOStrategy
import pandas as pd
import os
import sys
import logging
from collections import Counter

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, GridSearchCV, StratifiedKFold
from sklearn.metrics import (
    accuracy_score, classification_report, confusion_matrix,
    precision_recall_curve, roc_auc_score, balanced_accuracy_score,
    f1_score, precision_score, recall_score
)
from imblearn.over_sampling import SMOTE

# Existing imports and setup...
project_root = os.path.abspath(os.path.join(
    os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)


# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def prepare_data_for_feature_selection(df: pd.DataFrame, future_bars: int = 3, threshold: float = 0.005) -> tuple[pd.DataFrame, list]:
    """
    Prepare dataset for feature selection by creating a target variable
    based on future returns.

    Args:
        df (pd.DataFrame): DataFrame with OHLCV and indicators
        future_bars (int, optional): Number of bars ahead to predict. Defaults to 3.
        threshold (float, optional): Return threshold to consider a trade profitable. Defaults to 0.005.

    Returns:
        tuple[pd.DataFrame, list]: A tuple containing:
            - DataFrame with features and target variable
            - List of feature column names
    """
    # Calculate future returns
    df['future_return'] = df['close_price'].pct_change(
        future_bars).shift(-future_bars)

    # Create a binary target variable (1=profitable trade, 0=not profitable)
    df['target'] = (df['future_return'] > threshold).astype(int)

    # Drop rows with NaN values
    df = df.dropna()

    # Feature and target columns
    all_columns = df.columns
    feature_columns = [col for col in all_columns if col.endswith('_norm') or
                       (col not in ['target', 'future_return', 'timestamp', 'open_price',
                                    'high_price', 'low_price', 'close_price', 'volume'])]

    logger.info(f"Class distribution: {Counter(df['target'])}")

    return df, feature_columns


def run_feature_selection(df, feature_columns, target_column='target', random_state=42):
    """
    Run Random Forest feature selection with balanced class weights and hyperparameter tuning
    n_estimators parameter is not used directly in this function
    """
    """
    Run Random Forest feature selection with balanced class weights and hyperparameter tuning

    Args:
        df: DataFrame with features and target
        feature_columns: List of feature column names
        target_column: Target column name
        n_estimators: Number of trees in the forest
        random_state: Random seed for reproducibility

    Returns:
        DataFrame with feature importances and trained model
    """
    # Extract features and target
    X = df[feature_columns]
    y = df[target_column]

    # Split the data with stratification to maintain class distribution
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.25, random_state=random_state, stratify=y
    )

    # Print class distributions to verify stratification
    logger.info(f"Training set class distribution: {Counter(y_train)}")
    logger.info(f"Test set class distribution: {Counter(y_test)}")

    # Apply SMOTE to handle class imbalance
    smote = SMOTE(random_state=random_state)
    result = smote.fit_resample(X_train, y_train)
    X_train_resampled, y_train_resampled = result[0], result[1]

    logger.info(
        f"After SMOTE - Training class distribution: {Counter(y_train_resampled)}")

    # Define hyperparameter grid for RandomForest
    param_grid = {
        'n_estimators': [100, 200],
        'max_depth': [None, 10, 20],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4],
        'class_weight': ['balanced', 'balanced_subsample', None]
    }

    # Initialize RandomForest with class weights
    rf = RandomForestClassifier(random_state=random_state)

    # Use GridSearchCV with stratified k-fold for hyperparameter tuning
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=random_state)
    grid_search = GridSearchCV(
        rf, param_grid, cv=cv, scoring='f1', n_jobs=-1, verbose=1
    )

    # Fit the model
    logger.info("Starting GridSearchCV for hyperparameter tuning...")
    # Convert to numpy arrays to satisfy type checker
    # numpy is already imported at the top
    X_train_np = np.asarray(X_train_resampled)
    y_train_np = np.asarray(y_train_resampled)
    grid_search.fit(X_train_np, y_train_np)

    # Get best model
    best_rf = grid_search.best_estimator_
    logger.info(f"Best parameters: {grid_search.best_params_}")

    # Evaluate on test set
    y_pred = best_rf.predict(X_test)
    y_prob = best_rf.predict_proba(X_test)[:, 1]  # Probability for class 1

    # Calculate various metrics
    accuracy = accuracy_score(y_test, y_pred)
    balanced_acc = balanced_accuracy_score(y_test, y_pred)
    roc_auc = roc_auc_score(y_test, y_prob)

    print(f"Model Accuracy: {accuracy:.4f}")
    print(f"Balanced Accuracy: {balanced_acc:.4f}")
    print(f"ROC AUC Score: {roc_auc:.4f}")
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred))

    # Print confusion matrix
    cm = confusion_matrix(y_test, y_pred)
    print("\nConfusion Matrix:")
    print(cm)

    # Plot confusion matrix
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['Not Profitable', 'Profitable'],
                yticklabels=['Not Profitable', 'Profitable'])
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.title('Confusion Matrix')
    plt.savefig('confusion_matrix.png')

    # Find optimal threshold
    print("\nFinding optimal threshold based on precision-recall curve...")
    precision, recall, thresholds = precision_recall_curve(y_test, y_prob)

    # Calculate F1 score for each threshold
    f1_scores = 2 * (precision * recall) / (precision + recall + 1e-10)
    optimal_idx = np.argmax(f1_scores)
    optimal_threshold = thresholds[optimal_idx]

    print(f"Optimal threshold: {optimal_threshold:.4f}")
    print(
        f"At optimal threshold - Precision: {precision[optimal_idx]:.4f}, Recall: {recall[optimal_idx]:.4f}, F1: {f1_scores[optimal_idx]:.4f}")

    # Apply optimal threshold
    y_pred_optimal = (y_prob >= optimal_threshold).astype(int)
    print("\nClassification Report with optimal threshold:")
    print(classification_report(y_test, y_pred_optimal))

    # Get feature importances
    feature_importances = pd.DataFrame({
        'feature': X.columns,
        'importance': best_rf.feature_importances_
    }).sort_values('importance', ascending=False)

    return feature_importances, best_rf, optimal_threshold


def plot_feature_importance(feature_importances: pd.DataFrame,
                            title: str = "Feature Importance for Trading Indicators",
                            figsize: tuple[int, int] = (12, 10)) -> None:
    """
    Plot feature importances

    Args:
        feature_importances (pd.DataFrame): DataFrame with feature importances, must have 'feature' and 'importance' columns
        title (str, optional): Plot title. Defaults to "Feature Importance for Trading Indicators".
        figsize (tuple[int, int], optional): Figure size (width, height). Defaults to (12, 10).
    """
    plt.figure(figsize=figsize)
    sns.barplot(x='importance', y='feature', data=feature_importances.head(20))
    plt.title(title)
    plt.tight_layout()
    plt.savefig('feature_importance.png')
    plt.show()


def perform_advanced_cross_validation(df: pd.DataFrame,
                                      top_features: list[str],
                                      target_column: str = 'target',
                                      n_folds: int = 5,
                                      random_state: int = 42) -> dict[str, list[float]]:
    """
    Perform cross-validation with SMOTE and optimal threshold

    Args:
        df (pd.DataFrame): DataFrame with data
        top_features (list[str]): List of top feature names to use
        target_column (str, optional): Target column name. Defaults to 'target'.
        n_folds (int, optional): Number of cross-validation folds. Defaults to 5.
        random_state (int, optional): Random seed for reproducibility. Defaults to 42.

    Returns:
        dict[str, list[float]]: Dictionary containing lists of metrics across all folds
    """
    # These are already imported at the top

    X = df[top_features]
    y = df[target_column]

    # Initialize metrics storage
    metrics = {
        'accuracy': [],
        'balanced_accuracy': [],
        'precision': [],
        'recall': [],
        'f1': []
    }

    # Initialize StratifiedKFold
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True,
                          random_state=random_state)

    for fold, (train_idx, test_idx) in enumerate(skf.split(X, y)):
        X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]
        y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]

        # Apply SMOTE
        smote = SMOTE(random_state=random_state)
        result = smote.fit_resample(X_train, y_train)
        X_train_resampled, y_train_resampled = result[0], result[1]

        # Convert to numpy arrays for model compatibility
        X_train_resampled = np.asarray(X_train_resampled)
        y_train_resampled = np.asarray(y_train_resampled)

        # Train model with class weights
        rf = RandomForestClassifier(
            n_estimators=200,
            class_weight='balanced',
            random_state=random_state
        )
        rf.fit(X_train_resampled, y_train_resampled)

        # Predict probabilities
        y_prob = rf.predict_proba(X_test)[:, 1]

        # Find optimal threshold using precision-recall curve
        precision, recall, thresholds = precision_recall_curve(y_test, y_prob)
        f1_scores = 2 * (precision * recall) / (precision + recall + 1e-10)

        # Handle case where thresholds array is shorter than precision array
        if len(precision) > len(thresholds):
            # Add a 1.0 threshold for the highest precision point
            thresholds = np.append(thresholds, 1.0)

        optimal_idx = np.argmax(f1_scores)
        optimal_threshold = thresholds[optimal_idx]

        # Apply optimal threshold
        y_pred = (y_prob >= optimal_threshold).astype(int)

        # Calculate metrics
        metrics['accuracy'].append(accuracy_score(y_test, y_pred))
        metrics['balanced_accuracy'].append(
            balanced_accuracy_score(y_test, y_pred))
        metrics['precision'].append(precision_score(y_test, y_pred))
        metrics['recall'].append(recall_score(y_test, y_pred))
        metrics['f1'].append(f1_score(y_test, y_pred))

        print(
            f"Fold {fold+1} - Optimal threshold: {optimal_threshold:.4f}, F1: {metrics['f1'][-1]:.4f}")

    # Print average metrics
    for metric_name, values in metrics.items():
        print(
            f"Mean {metric_name}: {np.mean(values):.4f} ± {np.std(values):.4f}")

    return metrics


def select_top_features(feature_importances: pd.DataFrame, top_n: int = 15) -> list[str]:
    """
    Select top N most important features

    Args:
        feature_importances (pd.DataFrame): DataFrame with feature importances, must have 'feature' column
        top_n (int, optional): Number of top features to select. Defaults to 15.

    Returns:
        list[str]: List of top feature names
    """
    return feature_importances.head(top_n)['feature'].tolist()


def run_feature_selection_pipeline(df: pd.DataFrame,
                                   future_bars: int = 3,
                                   threshold: float = 0.01,
                                   top_n: int = 20) -> tuple[list[str], RandomForestClassifier, float]:
    """
    Full pipeline for feature selection with improved handling of class imbalance

    Args:
        df (pd.DataFrame): Input DataFrame with OHLCV and indicator data
        future_bars (int, optional): Number of bars to predict ahead. Defaults to 3.
        threshold (float, optional): Return threshold for profitable trade. Defaults to 0.01.
        top_n (int, optional): Number of top features to select. Defaults to 20.

    Returns:
        tuple[list[str], RandomForestClassifier, float]: A tuple containing:
            - List of selected feature names
            - Trained RandomForest model
            - Optimal prediction threshold
    """

    # Prepare data
    prepared_df, feature_columns = prepare_data_for_feature_selection(
        df, future_bars, threshold)

    # Run feature selection with improved handling
    feature_importances, model, optimal_threshold = run_feature_selection(
        prepared_df, feature_columns)

    # Plot feature importance
    plot_feature_importance(feature_importances)

    # Select top features
    top_features = select_top_features(feature_importances, top_n)

    # Validate selection with advanced cross-validation
    perform_advanced_cross_validation(prepared_df, top_features)

    # Feature correlation analysis
    correlation_analysis(prepared_df, top_features)

    return top_features, model, optimal_threshold


def correlation_analysis(df: pd.DataFrame, top_features: list[str]) -> None:
    """
    Analyze correlation between top features to identify redundancy

    Args:
        df (pd.DataFrame): Input DataFrame containing the features
        top_features (list[str]): List of feature names to analyze
    """
    # Add target to features for correlation analysis
    analysis_columns = top_features + ['target']

    # Calculate correlation matrix
    corr_matrix = df[analysis_columns].corr()

    # Plot correlation heatmap
    plt.figure(figsize=(12, 10))
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', fmt='.2f',
                linewidths=0.5, vmin=-1, vmax=1)
    plt.title('Feature Correlation Heatmap')
    plt.tight_layout()
    plt.savefig('feature_correlation.png')

    # Identify highly correlated features
    print("\nHighly correlated feature pairs (|corr| > 0.7):")
    high_corr_pairs = []

    for i in range(len(top_features)):
        for j in range(i+1, len(top_features)):
            val = corr_matrix.iloc[i, j]
            # Safely convert val to float if possible, else skip or set to 0
            if isinstance(val, complex):
                val = val.real
            if isinstance(val, (int, float, np.number)):
                corr = np.abs(float(val))
            else:
                corr = 0.0
            if corr > 0.7:
                high_corr_pairs.append(
                    (top_features[i], top_features[j], corr))

    # Sort by correlation strength
    high_corr_pairs.sort(key=lambda x: x[2], reverse=True)

    # Print correlated pairs
    for feat1, feat2, corr in high_corr_pairs:
        print(f"{feat1} - {feat2}: {corr:.4f}")


def feature_elimination_analysis(df: pd.DataFrame,
                                 feature_columns: list[str],
                                 target_column: str = 'target',
                                 random_state: int = 42) -> list[str]:
    """
    Perform recursive feature elimination to find optimal feature set

    Args:
        df (pd.DataFrame): Input DataFrame containing features and target
        feature_columns (list[str]): List of feature column names to consider
        target_column (str, optional): Name of the target column. Defaults to 'target'.
        random_state (int, optional): Random seed for reproducibility. Defaults to 42.
    """
    from sklearn.feature_selection import RFECV

    X = df[feature_columns]
    y = df[target_column]

    # Apply SMOTE to balance classes
    smote = SMOTE(random_state=random_state)
    result = smote.fit_resample(X, y)
    X_resampled, y_resampled = result[0], result[1]

    # Convert to numpy arrays for model compatibility
    X_resampled = np.asarray(X_resampled)
    y_resampled = np.asarray(y_resampled)

    # Initialize model
    rf = RandomForestClassifier(
        n_estimators=100,
        class_weight='balanced',
        random_state=random_state
    )

    # Initialize RFECV
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=random_state)
    rfecv = RFECV(
        estimator=rf,
        step=1,
        cv=cv,
        scoring='f1',
        min_features_to_select=5,
        n_jobs=-1,
        verbose=1
    )

    # Fit RFECV
    print("Starting Recursive Feature Elimination with Cross-Validation...")
    rfecv.fit(X_resampled, y_resampled)

    # Print results
    print(f"Optimal number of features: {rfecv.n_features_}")

    # Plot number of features vs CV score
    plt.figure(figsize=(10, 6))
    plt.xlabel("Number of features selected")
    plt.ylabel("Cross-validation F1 score")
    # Use cv_results_ which is available in newer sklearn versions
    plt.plot(range(1, len(rfecv.cv_results_['mean_test_score']) + 1),
             rfecv.cv_results_['mean_test_score'])

    plt.title("RFECV - Number of Features vs. F1 Score")
    plt.savefig('rfecv_scores.png')

    # Get selected features
    selected_features = [feature for feature, selected in zip(
        X.columns, rfecv.support_) if selected]
    print("\nSelected features by RFECV:")
    for i, feature in enumerate(selected_features, 1):
        print(f"{i}. {feature}")

    return selected_features


def save_model_and_features(model: RandomForestClassifier,
                            top_features: list[str],
                            optimal_threshold: float,
                            output_path: str = 'trading_model') -> None:
    """
    Save the trained model, selected features, and optimal threshold

    Args:
        model (RandomForestClassifier): Trained RandomForest model
        top_features (list[str]): List of selected feature names
        optimal_threshold (float): Optimal threshold for prediction
        output_path (str, optional): Base path for saving files. Defaults to 'trading_model'.
    """
    import joblib
    import json
    # os is already imported at the top

    # Create directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)

    # Save model
    joblib.dump(model, os.path.join(output_path, 'model.joblib'))

    # Save features and threshold
    with open(os.path.join(output_path, 'model_metadata.json'), 'w', encoding='utf-8') as f:
        json.dump({
            'top_features': top_features,
            'optimal_threshold': optimal_threshold
        }, f, indent=4)

    print(f"Model and metadata saved to {output_path}/")


def load_and_prepare_data(file_path: str, pair_name: str) -> pd.DataFrame:
    """
    Load and prepare OHLC data from a CSV file

    Args:
        file_path (str): Path to the CSV file containing OHLC data
        pair_name (str): Name of the trading pair (e.g., 'SOLUSD')

    Returns:
        pd.DataFrame: Processed DataFrame with OHLC data and technical indicators
    """
    logger.info(f"Loading data from {file_path}")

    # Load your Kraken OHLC data
    df = pd.read_csv(file_path)

    # Make sure the columns are correctly named
    expected_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']

    # Rename columns if needed
    column_mapping = {}
    for expected, actual in zip(expected_columns, df.columns):
        column_mapping[actual] = expected

    df = df.rename(columns=column_mapping)

    # Convert timestamp to datetime if it's not already
    if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')

    # Normalize column names to match what the strategy expects
    df = df.rename(columns={
        'open': 'open_price',
        'high': 'high_price',
        'low': 'low_price',
        'close': 'close_price'
    })

    # Use the PPO strategy's preprocessing logic
    ppo_strategy = PPOStrategy(pair=pair_name)
    df = ppo_strategy.preprocess_data(df)

    return df


if __name__ == "__main__":
    # Update with your file path
    data_file = "/Users/<USER>/Business/Software projects/pyTrader/ohlc_data/SOLUSD_60.csv"
    # Load and prepare data
    df = load_and_prepare_data(data_file, 'SOLUSD')

    # List of all columns used in your observation space (including both normalized and pattern columns)
    required_columns = [
        'timestamp',
        'open_price',
        'high_price',
        'low_price',
        'close_price',
        'volume',

        # Price and volume basics
        'open_price_norm',
        'high_price_norm',
        'low_price_norm',
        'close_price_norm',
        'volume_norm',

        # Trend indicators
        'EMA_short_norm',
        'EMA_long_norm',
        'macd_norm',
        'macd_signal_norm',
        'macd_hist_norm',
        'sar_norm',

        # Momentum indicators
        'rsi_norm',
        'momentum_norm',
        'stoch_k_norm',
        'stoch_d_norm',
        'cci_norm',
        'roc_norm',
        'mfi_norm',

        # Volatility indicators
        'adx_norm',
        'atr_norm',
        'bb_upper_norm',
        'bb_middle_norm',
        'bb_lower_norm',
        'bb_percent_b_norm',
        'volatility_ratio_norm',

        # Volume indicators
        'vwap_norm',
        'obv_norm',
        'relative_volume_norm',
        'volume_roc_norm',
        'stddev_returns_norm',
        'ad_line_norm',
        'adosc_norm',

        # Volatility bands
        'kc_upper_norm',
        'kc_middle_norm',
        'kc_lower_norm',
        'vwap_slope_norm',
        'obv_slope_norm',

        # Market regime
        'market_regime',

        # Candlestick patterns
        'bullish_engulfing',
        'bearish_engulfing',
        'hammer',
        'hanging_man',
        'doji',
        'morning_star',
        'evening_star',
        'marubozu',
        'three_white_soldiers',
        'three_black_crows'
    ]

    # Filter the DataFrame to only include these columns
    filtered_df = df[required_columns].copy()

    # Set parameters
    future_bars = 3  # Predict 3 bars ahead
    threshold = 0.01  # 1% return threshold for profitable trade
    top_n = 20  # Select top 20 features (reduced from 49)

    # Run the improved pipeline
    top_features, best_model, optimal_threshold = run_feature_selection_pipeline(
        filtered_df, future_bars, threshold, top_n)

    # Perform feature elimination analysis
    selected_features = feature_elimination_analysis(
        filtered_df, top_features, 'target')

    # Save the model and selected features
    save_model_and_features(best_model, selected_features, optimal_threshold)

    print("\nTop selected features:")
    for i, feature in enumerate(top_features, 1):
        print(f"{i}. {feature}")
