import asyncio
import pandas as pd
from datetime import datetime
from typing import Optional
from app.db.db_executor import DatabaseExecutor
from app.scripts.base_data_feed import DataFeed


class HistoricalDataFeed(DataFeed):
    def __init__(self, db: DatabaseExecutor, pair: str, start_date: datetime, end_date: datetime, preprocess_func, order_book_fetcher=None):
        """
        Initialize the HistoricalDataFeed.

        Args:
            db (DatabaseExecutor): Database executor instance for querying data.
            pair (str): Trading pair (e.g., 'XBTUSD').
            start_date (datetime): Start date for the data range.
            end_date (datetime): End date for the data range.
            preprocess_func (callable): Function to preprocess the DataFrame (e.g., strategy.preprocess_data).
            order_book_fetcher (callable, optional): Function to fetch order book data for a given time range.
        """
        self.db = db
        self.pair = pair
        self.start_date = start_date
        self.end_date = end_date
        self.preprocess_func = preprocess_func
        self.order_book_fetcher = order_book_fetcher
        self.candles = None  # Will store the preprocessed DataFrame
        self.index = 0  # Tracks the current position in the candle sequence

    async def connect(self):
        """
        Fetch and preprocess historical data from the database.
        This method runs the database query in a separate thread to avoid blocking the event loop.
        """
        query = """
        SELECT timestamp, open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp BETWEEN %s AND %s
        ORDER BY timestamp
        """
        # Execute the query in a separate thread since db.execute_select is synchronous
        results = await asyncio.to_thread(
            self.db.execute_select,
            query,
            (self.pair, self.start_date, self.end_date),
            # Convert price and volume columns to float
            float_columns=[1, 2, 3, 4, 5]
        )
        # Create a DataFrame from the query results
        df = pd.DataFrame(
            results,
            columns=['timestamp', 'open_price', 'high_price',
                     'low_price', 'close_price', 'volume']
        )

        # If order_book_fetcher is provided, fetch order book data and merge features
        if self.order_book_fetcher is not None:
            order_book_data = await self.order_book_fetcher(self.start_date, self.end_date)
            if order_book_data is not None and not order_book_data.empty:
                # Import the new preprocessing function
                from app.utils.order_book_preprocessing import merge_partial_order_book_snapshots

                # Preprocess order book data to merge partial snapshots within 10 seconds
                order_book_data = merge_partial_order_book_snapshots(order_book_data, time_window_seconds=10)

                # Merge order book features into OHLC dataframe on timestamp
                df = pd.merge_asof(df.sort_values('timestamp'),
                                   order_book_data.sort_values('timestamp'),
                                   on='timestamp',
                                   direction='backward',
                                   tolerance=pd.Timedelta('1min'))  # Adjust tolerance as needed
        # Apply the preprocessing function to the DataFrame
        self.candles = self.preprocess_func(df)
        self.index = 0  # Reset index for iteration

    async def get_next_candle(self) -> Optional[pd.Series]:
        """
        Retrieve the next candle from the preprocessed data.

        Returns:
            Optional[pd.Series]: The next candle as a pandas Series, or None if no more candles are available.
        """
        if self.candles is not None and self.index < len(self.candles):
            candle = self.candles.iloc[self.index]
            self.index += 1
            return candle
        return None
