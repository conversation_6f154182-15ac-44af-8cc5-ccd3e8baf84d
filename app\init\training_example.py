"""
Example script showing how to use the enhanced training_model.py with database integration

This example demonstrates:
1. How to configure database credentials
2. How to set up training parameters
3. How to run training with order book features
4. How to handle different data sources
"""

import asyncio
import os
from datetime import datetime, timezone
import logging

# Import the training functions
from training_model import (
    get_database_connection,
    load_and_prepare_data_from_db,
    load_and_prepare_data_from_csv,
    train_model,
    evaluate_model
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_database_training():
    """Example of training with database and order book features"""
    
    # Configuration
    pair_name = "SOLUSD"
    start_date = datetime(2024, 6, 1, tzinfo=timezone.utc)
    end_date = datetime(2024, 12, 31, tzinfo=timezone.utc)
    
    logger.info("=== Database Training Example ===")
    
    try:
        # Initialize database connection
        # Make sure to update credentials in get_database_connection()
        db = get_database_connection()
        
        # Load data with order book features
        logger.info("Loading data from database with order book features...")
        df = await load_and_prepare_data_from_db(
            db=db,
            pair_name=pair_name,
            start_date=start_date,
            end_date=end_date,
            include_order_book=True
        )
        
        # Close database connection
        db.close()
        
        # Check what features we have
        order_book_features = [col for col in df.columns if col.startswith('ob_')]
        logger.info(f"Order book features found: {order_book_features}")
        
        # Split data
        split_idx = int(len(df) * 0.8)
        train_df = df.iloc[:split_idx].copy()
        test_df = df.iloc[split_idx:].copy()
        
        logger.info(f"Training data: {len(train_df)} rows")
        logger.info(f"Testing data: {len(test_df)} rows")
        
        # Train model (reduced timesteps for example)
        model, model_path = train_model(train_df, pair_name, timesteps=10000)
        
        if model:
            # Evaluate model
            results = evaluate_model(model, test_df, pair_name)
            logger.info(f"Training completed successfully!")
            logger.info(f"Model performance: {results}")
        
    except Exception as e:
        logger.error(f"Database training failed: {e}")


async def example_csv_training():
    """Example of training with CSV file (fallback method)"""
    
    logger.info("=== CSV Training Example ===")
    
    # Configuration
    pair_name = "SOLUSD"
    csv_file = "path/to/your/SOLUSD_60.csv"  # Update this path
    
    try:
        # Load data from CSV
        df = load_and_prepare_data_from_csv(csv_file, pair_name)
        
        # Split data
        split_idx = int(len(df) * 0.8)
        train_df = df.iloc[:split_idx].copy()
        test_df = df.iloc[split_idx:].copy()
        
        logger.info(f"Training data: {len(train_df)} rows")
        logger.info(f"Testing data: {len(test_df)} rows")
        
        # Train model (reduced timesteps for example)
        model, model_path = train_model(train_df, pair_name, timesteps=10000)
        
        if model:
            # Evaluate model
            results = evaluate_model(model, test_df, pair_name)
            logger.info(f"Training completed successfully!")
            logger.info(f"Model performance: {results}")
        
    except Exception as e:
        logger.error(f"CSV training failed: {e}")


async def main():
    """Run training examples"""
    
    print("PPO Training Examples")
    print("=" * 50)
    
    # Example 1: Database training with order book features
    print("\n1. Database Training (with order book features)")
    print("-" * 50)
    await example_database_training()
    
    # Example 2: CSV training (traditional method)
    print("\n2. CSV Training (traditional method)")
    print("-" * 50)
    await example_csv_training()
    
    print("\nExamples completed!")


if __name__ == "__main__":
    asyncio.run(main())
