import asyncio
import json
import logging
import random
import time
from typing import Any, Optional
from collections.abc import Callable
from dataclasses import dataclass, field
from enum import Enum
import websockets
from websockets.protocol import State

logger = logging.getLogger(__name__)

class ChannelType(str, Enum):
    BOOK = "book"
    OHLC = "ohlc"
    OPEN_ORDERS = "openOrders"
    OWN_TRADES = "ownTrades"
    SPREAD = "spread"
    TICKER = "ticker"
    TRADE = "trade"
    ALL = "*"

class KrakenWebSocketV2:
    """Enhanced Kraken WebSocket API v2 client with improved reconnection strategy"""

    def __init__(
        self,
        credentials: Optional[Any] = None,
        public_url: str = "wss://ws.kraken.com/v2",
        private_url: str = "wss://ws-auth.kraken.com/v2",
        reconnect_interval: float = 5.0,
        max_reconnect_interval: float = 300.0,
        ping_interval: float = 30.0,
        ping_timeout: float = 10.0,
        target_symbol: str = "BTC/USD"
    ):
        self.credentials = credentials
        self.public_url = public_url
        self.private_url = private_url
        self.reconnect_interval = reconnect_interval
        self.max_reconnect_interval = max_reconnect_interval
        self.ping_interval = ping_interval
        self.ping_timeout = ping_timeout
        self.target_symbol = target_symbol

        # Connection management
        self.public_ws: Optional[Any] = None
        self.private_ws: Optional[Any] = None
        self.running: bool = False
        self._reconnect_lock: asyncio.Lock = asyncio.Lock()
        self._connection_event = asyncio.Event()
        self._last_pong_time: Optional[float] = None
        self._reconnect_attempts: int = 0

        # Message handling
        self.subscriptions: dict[str, Callable[[dict[str, Any]], None]] = {}
        self.pending_subscriptions: dict[str, dict[str, Any]] = {}
        self.connection_listeners: set[Any] = set()

        # Tasks
        self.public_message_task: Optional[asyncio.Task] = None
        self.private_message_task: Optional[asyncio.Task] = None
        self.ping_task: Optional[asyncio.Task] = None
        self.health_task: Optional[asyncio.Task] = None

        # Request ID counter
        self._req_id_counter: int = 0
    def _get_next_req_id(self) -> int:
        """Get next request ID"""
        self._req_id_counter += 1
        return self._req_id_counter

    async def connect_public(self) -> None:
        """Establish public websocket connection with improved error handling"""
        async with self._reconnect_lock:
            if self.public_ws is not None and self.public_ws.state == State.OPEN:
                return  # Already connected

            try:
                logger.info(f"Attempting to connect to {self.public_url}")

                # Close existing connection if any
                if self.public_ws:
                    try:
                        await self.public_ws.close()
                    except Exception:
                        pass

                # Create new connection with proper timeout
                self.public_ws = await asyncio.wait_for(
                    websockets.connect(
                        self.public_url,
                        ping_interval=None,  # We'll handle pings manually
                        ping_timeout=None,
                        close_timeout=10,
                        max_size=2**20,  # 1MB max message size
                        compression=None
                    ),
                    timeout=30.0  # 30 second connection timeout
                )

                logger.info("Connected to Kraken public websocket")
                self._reconnect_attempts = 0
                self._last_pong_time = time.time()
                self._connection_event.set()

                # Start tasks
                await self._start_connection_tasks()

                # Notify listeners
                for listener in self.connection_listeners:
                    try:
                        await listener.on_websocket_connected()
                    except Exception as e:
                        logger.error(
                            f"Error notifying connection listener: {e}")

                # Send pending subscriptions
                await self._send_pending_subscriptions()

                self.running = True

            except Exception as e:
                logger.error(f"Failed to connect to public WebSocket: {e}")
                self._connection_event.clear()
                # Removed double increment of reconnect attempts here
                raise

    async def _start_connection_tasks(self) -> None:
        """Start all connection-related tasks"""
        # Cancel existing tasks
        await self._cancel_tasks()

        # Start new tasks
        self.public_message_task = asyncio.create_task(
            self._process_public_messages(),
            name="public_message_processor"
        )

        self.ping_task = asyncio.create_task(
            self._ping_loop(),
            name="ping_loop"
        )

        self.health_task = asyncio.create_task(
            self._health_check_loop(),
            name="health_check"
        )

        logger.info("Started connection tasks")

    async def _send_pending_subscriptions(self) -> None:
        """Send all pending subscriptions"""
        if not self.pending_subscriptions:
            return

        for key, sub in list(self.pending_subscriptions.items()):
            try:
                if self.public_ws is not None:
                    await self.public_ws.send(json.dumps(sub))
                    logger.info(f"Sent pending subscription: {key}")
                else:
                    logger.error(
                        f"Public websocket is None, cannot send subscription {key}")
                    break
            except Exception as e:
                logger.error(f"Failed to send pending subscription {key}: {e}")
                break  # Stop sending if connection is broken

        self.pending_subscriptions.clear()

    async def _process_public_messages(self) -> None:
        """Process incoming public messages with improved error handling"""
        logger.info("Started message processing")

        while self.running and self.public_ws and self.public_ws.state == State.OPEN:
            try:
                # Add ping check to detect stale connections
                if self._last_pong_time is None or time.time() - self._last_pong_time > self.ping_interval + self.ping_timeout:
                    logger.warning("No recent pong, reconnecting...")
                    break

                # Wait for message with timeout
                message = await asyncio.wait_for(
                    self.public_ws.recv(),
                    timeout=60.0  # 1 minute timeout
                )

                await self._handle_message(message)

            except asyncio.TimeoutError:
                logger.warning(
                    "Message receive timeout - connection may be stale")
                break
            except websockets.ConnectionClosed as e:
                logger.warning(
                    f"WebSocket connection closed: {e.code} {e.reason}")
                break
            except Exception as e:
                logger.error(f"Error processing message: {e}", exc_info=True)
                # Don't break on message processing errors, continue receiving

        logger.info("Message processing stopped")
        await self._handle_disconnection()

    async def _handle_message(self, message: str) -> None:
        """Handle individual message"""
        try:
            message_data = json.loads(message)

            # Update last_pong_time on ANY message received (not just pong)
            self._last_pong_time = time.time()

            # Handle pong responses
            if message_data.get("method") == "pong":
                logger.debug("Received pong response")
                return

            # Handle subscription responses
            if "result" in message_data:
                logger.info(f"Subscription result: {message_data}")
                return

            # Handle errors
            if "error" in message_data:
                logger.error(f"WebSocket error: {message_data}")
                return

            # Route to appropriate callback
            await self._route_message(message_data)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse message: {e}")
        except Exception as e:
            logger.error(f"Error handling message: {e}", exc_info=True)

    async def _route_message(self, message_data: dict) -> None:
        """Route message to appropriate callback"""
        channel = message_data.get("channel")
        symbol = message_data.get("symbol")

        # Extract symbol from data array if not in top level
        if not symbol and "data" in message_data and isinstance(message_data["data"], list) and message_data["data"]:
            first_item = message_data["data"][0]
            if isinstance(first_item, dict) and "symbol" in first_item:
                symbol = first_item["symbol"]

        if not channel:
            return

        # Find callback
        callback = None
        if symbol:
            callback = self.subscriptions.get(f"{channel}-{symbol}")
        if not callback:
            callback = self.subscriptions.get(f"{channel}-*")

        if callback:
            try:
                callback(message_data)
            except Exception as e:
                logger.error(f"Error in callback for {channel}-{symbol}: {e}")
        else:
            if channel not in {"status", "heartbeat", "systemStatus"}:
                logger.debug(f"No callback for {channel}-{symbol}")

    async def _ping_loop(self) -> None:
        """Send periodic pings to keep connection alive"""
        while self.running and self.public_ws and self.public_ws.state == State.OPEN:
            try:
                await asyncio.sleep(self.ping_interval)
                if self.public_ws and self.public_ws.state == State.OPEN:
                    ping_msg = {
                        "method": "ping",
                        "req_id": self._get_next_req_id()
                    }
                    await self.public_ws.send(json.dumps(ping_msg))
                    logger.debug("Sent ping message")
            except Exception as e:
                logger.error(f"Error in ping loop: {e}")
                # Instead of breaking, sleep and retry
                await asyncio.sleep(self.ping_interval)
                continue

    async def _health_check_loop(self) -> None:
        """Monitor connection health and trigger reconnection if needed"""
        while self.running:
            try:
                await asyncio.sleep(self.ping_interval + self.ping_timeout)
                # Check if connection is healthy
                if not self._is_connection_healthy():
                    logger.warning("Connection health check failed")
                    await self._handle_disconnection()
                    # Do not break; allow loop to continue after reconnection
                    continue
            except Exception as e:
                logger.error(f"Error in health check: {e}")
                await asyncio.sleep(30)  # Wait before retrying
                continue

    def _is_connection_healthy(self) -> bool:
        """Check if connection is healthy"""
        if not self.public_ws or self.public_ws.state != State.OPEN:
            return False

        # Check if we received a pong recently
        if self._last_pong_time:
            time_since_pong = time.time() - self._last_pong_time
            if time_since_pong > (self.ping_interval + self.ping_timeout):
                logger.warning(
                    f"No pong received for {time_since_pong:.1f} seconds")
                return False

        return True

    async def _handle_disconnection(self) -> None:
        """Handle disconnection and trigger reconnection"""
        logger.warning("WebSocket disconnected")
        self._connection_event.clear()

        # Cancel tasks
        await self._cancel_tasks()

        # Notify listeners
        for listener in self.connection_listeners:
            try:
                await listener.on_websocket_disconnected()
            except Exception as e:
                logger.error(f"Error notifying disconnection listener: {e}")

        # Start reconnection if still running
        if self.running:
            asyncio.create_task(self._attempt_reconnect())

    async def _attempt_reconnect(self) -> None:
        """Attempt to reconnect with exponential backoff"""
        # Don't start multiple reconnection attempts
        if self._reconnect_lock.locked():
            logger.debug("Reconnection already in progress, skipping")
            return

        async with self._reconnect_lock:
            logger.info("Starting reconnection process")

            # Reset reconnect attempts if we had a successful connection
            if self._reconnect_attempts > 0 and self.is_public_connected():
                self._reconnect_attempts = 0

            while self.running and not self.is_public_connected():
                try:
                    # Calculate delay with exponential backoff and jitter
                    delay = min(
                        self.reconnect_interval * (2 ** min(self._reconnect_attempts, 10)),
                        self.max_reconnect_interval
                    )
                    # Add jitter to prevent thundering herd
                    delay = delay * (0.8 + 0.4 * random.random())

                    logger.info(
                        f"Reconnecting in {delay:.1f} seconds (attempt {self._reconnect_attempts + 1})")
                    await asyncio.sleep(delay)

                    if not self.running:
                        break

                    # Try to connect
                    await self.connect_public()

                    if self.is_public_connected():
                        logger.info("Reconnection successful")
                        self._reconnect_attempts = 0  # Reset on success
                        return

                except asyncio.CancelledError:
                    logger.info("Reconnection cancelled")
                    raise
                except Exception as e:
                    logger.warning(f"Reconnection attempt {self._reconnect_attempts + 1} failed: {e}")
                    self._reconnect_attempts += 1

                    # Log a critical error after many attempts, but do not set self.running = False
                    if self._reconnect_attempts >= 10:
                        logger.critical("Too many reconnection attempts, still retrying...")
                        # Optionally: send an alert/notification here
                        # Continue retrying indefinitely
                        await asyncio.sleep(self.max_reconnect_interval)

    async def _cancel_tasks(self) -> None:
        """Cancel all connection tasks"""
        tasks = [
            self.public_message_task,
            self.private_message_task,
            self.ping_task,
            self.health_task
        ]

        for task in tasks:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

    def is_public_connected(self) -> bool:
        """Check if the public websocket connection is open"""
        return (self.public_ws is not None and
                self.public_ws.state == State.OPEN and
                self._connection_event.is_set())

    async def wait_for_connection(self, timeout: float = 30.0) -> bool:
        """Wait for connection to be established"""
        try:
            await asyncio.wait_for(self._connection_event.wait(), timeout=timeout)
            return True
        except asyncio.TimeoutError:
            return False

    async def subscribe(
        self,
        channel: Any,  # ChannelType
        symbols: list[str],
        callback: Callable[[dict[str, Any]], None],
        **params: Any
    ) -> None:
        """Subscribe to a channel with improved error handling"""
        req_id = self._get_next_req_id()

        # Build subscription request
        params_dict = {"channel": channel.value}
        if hasattr(channel, 'value') and channel.value != "instrument":
            params_dict["symbol"] = symbols
        params_dict.update(params)

        subscription = {
            "method": "subscribe",
            "params": params_dict,
            "req_id": req_id
        }

        # Determine callback key
        if hasattr(channel, 'value') and channel.value == "instrument":
            key = f"{channel.value}-*"
        elif len(symbols) == 1:
            key = f"{channel.value}-{symbols[0]}"
        else:
            key = f"{channel.value}-*"

        self.subscriptions[key] = callback

        # Send subscription if connected
        if self.is_public_connected():
            try:
                if self.public_ws is not None and self.public_ws.state == State.OPEN:
                    await self.public_ws.send(json.dumps(subscription))
                    logger.info(f"Sent subscription for {key}")
                else:
                    # ADD STATE VALIDATION
                    logger.warning("WebSocket not in OPEN state")
                    self.pending_subscriptions[key] = subscription
            except Exception as e:
                logger.error(f"Failed to send subscription {key}: {e}")
                self.pending_subscriptions[key] = subscription
        else:
            self.pending_subscriptions[key] = subscription
            logger.info(f"Stored pending subscription for {key}")

    async def unsubscribe(
        self,
        channel: Any,  # ChannelType
        symbols: list[str],
        **params: Any
    ) -> None:
        """Unsubscribe from a channel with improved error handling"""
        req_id = self._get_next_req_id()

        # Build unsubscription request
        params_dict = {"channel": channel.value}
        if hasattr(channel, 'value') and channel.value != "instrument":
            params_dict["symbol"] = symbols
        params_dict.update(params)

        unsubscription = {
            "method": "unsubscribe",
            "params": params_dict,
            "req_id": req_id
        }

        # Determine key to remove callback
        if hasattr(channel, 'value') and channel.value == "instrument":
            key = f"{channel.value}-*"
        elif len(symbols) == 1:
            key = f"{channel.value}-{symbols[0]}"
        else:
            key = f"{channel.value}-*"

        # Remove callback if exists
        self.subscriptions.pop(key, None)

        # Send unsubscription if connected
        if self.is_public_connected():
            try:
                if self.public_ws is not None and self.public_ws.state == State.OPEN:
                    await self.public_ws.send(json.dumps(unsubscription))
                    logger.info(f"Sent unsubscription for {key}")
                else:
                    logger.warning("WebSocket not in OPEN state")
            except Exception as e:
                logger.error(f"Failed to send unsubscription {key}: {e}")
        else:
            logger.info(f"Cannot send unsubscription for {key}, not connected")

    async def close(self) -> None:
        """Close connections and clean up"""
        logger.info("Closing WebSocket connections")
        self.running = False
        self._connection_event.clear()

        # Cancel all tasks
        await self._cancel_tasks()

        # Close connections

        if self.public_ws:
            try:
                await self.public_ws.close()
            except Exception:
                pass
        if self.private_ws:
            try:
                await self.private_ws.close()
            except Exception:
                pass

        logger.info("WebSocket connections closed")
