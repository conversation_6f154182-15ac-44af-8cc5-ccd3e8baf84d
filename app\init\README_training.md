# Enhanced PPO Training with Database Integration

This enhanced training script allows you to train PPO models using both OHLC data and order book features from your PostgreSQL database.

## Features

- **Database Integration**: Load OHLC data from `kraken_ohlc` table
- **Order Book Features**: Automatically fetch and calculate order book features from `order_book_snapshots` table
- **Automatic Fallback**: Falls back to CSV loading if database fails
- **Feature Engineering**: Calculates 12+ order book features including spread, depth, volume imbalance
- **Enhanced Observations**: PPO model gets richer state representation with market microstructure data

## Database Requirements

### Required Tables

1. **kraken_ohlc**
   ```sql
   CREATE TABLE kraken_ohlc (
       timestamp TIMESTAMP WITH TIME ZONE,
       open_price DECIMAL,
       high_price DECIMAL,
       low_price DECIMAL,
       close_price DECIMAL,
       volume DECIMAL,
       pair VARCHAR(10)
   );
   ```

2. **order_book_snapshots**
   ```sql
   CREATE TABLE order_book_snapshots (
       pair VARCHAR(10),
       snapshot_time TIMESTAMP WITH TIME ZONE,
       bids J<PERSON><PERSON><PERSON>,
       asks <PERSON><PERSON><PERSON><PERSON>,
       checksum BIGINT
   );
   ```

## Setup Instructions

### 1. Configure Database Credentials

Edit the `get_database_connection()` function in `training_model.py`:

```python
def get_database_connection():
    return DatabaseExecutor(
        db_name="your_database_name",    # Your database name
        db_user="your_username",         # Your username
        db_password="your_password",     # Your password
        db_host="localhost",             # Your host
        db_port=5432,                    # Your port
        logger=logger
    )
```

**Security Tip**: Use environment variables for credentials:
```python
import os
return DatabaseExecutor(
    db_name=os.getenv('DB_NAME', 'trading_db'),
    db_user=os.getenv('DB_USER', 'postgres'),
    db_password=os.getenv('DB_PASSWORD'),
    db_host=os.getenv('DB_HOST', 'localhost'),
    db_port=int(os.getenv('DB_PORT', '5432')),
    logger=logger
)
```

### 2. Configure Training Parameters

Edit the configuration section in the `main()` function:

```python
# Trading pair configuration
pair_name = "SOLUSD"  # Your trading pair

# Data source configuration
use_database = True  # Set to True to use database
include_order_book_features = True  # Include order book features

# Date range for database loading
start_date = datetime(2024, 1, 1, tzinfo=timezone.utc)
end_date = datetime(2024, 12, 31, tzinfo=timezone.utc)

# Training configuration
train_test_split = 0.8  # 80% training, 20% testing
training_timesteps = 100000  # Adjust based on your needs
```

## Order Book Features

The script automatically calculates these order book features:

1. **ob_spread** - Bid-ask spread (absolute)
2. **ob_spread_pct** - Bid-ask spread (percentage)
3. **ob_mid_price** - Mid price between best bid/ask
4. **ob_volume_imbalance** - Volume imbalance ratio (-1 to 1)
5. **ob_bid_depth_5** - Total volume in top 5 bid levels
6. **ob_ask_depth_5** - Total volume in top 5 ask levels
7. **ob_total_bid_vol** - Total bid volume
8. **ob_total_ask_vol** - Total ask volume
9. **ob_bid_impact** - Price impact for 1000 unit buy
10. **ob_ask_impact** - Price impact for 1000 unit sell
11. **ob_weighted_bid** - Volume-weighted bid price
12. **ob_weighted_ask** - Volume-weighted ask price

## Usage

### Basic Usage

```bash
cd app/init
python training_model.py
```

### Advanced Usage

```python
import asyncio
from training_model import load_and_prepare_data_from_db, get_database_connection

async def custom_training():
    db = get_database_connection()
    
    # Load data with order book features
    df = await load_and_prepare_data_from_db(
        db=db,
        pair_name="SOLUSD",
        start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
        end_date=datetime(2024, 12, 31, tzinfo=timezone.utc),
        include_order_book=True
    )
    
    # Your custom training logic here
    
    db.close()

asyncio.run(custom_training())
```

## Benefits of Order Book Features

1. **Market Microstructure**: Captures supply/demand dynamics
2. **Liquidity Information**: Spread and depth indicate market liquidity
3. **Price Impact**: Helps model understand execution costs
4. **Volume Imbalance**: Leading indicator of price movements
5. **Enhanced Decisions**: PPO model gets richer state representation

## Troubleshooting

### Database Connection Issues
- Verify database credentials
- Check if database is running
- Ensure tables exist and have data
- Check network connectivity

### No Order Book Data
- Verify `order_book_snapshots` table has data for your date range
- Check if your data collection is working
- Consider reducing the date range for testing

### Memory Issues
- Reduce `training_timesteps`
- Use smaller date ranges
- Consider data sampling

### Performance Tips
- Index your database tables on timestamp and pair columns
- Use appropriate date ranges (don't load years of data at once)
- Monitor memory usage during training
- Consider using SSD storage for better I/O performance

## Example Output

```
2024-01-15 10:30:00 - INFO - Loading data from database for SOLUSD...
2024-01-15 10:30:00 - INFO - Date range: 2024-01-01 00:00:00+00:00 to 2024-12-31 00:00:00+00:00
2024-01-15 10:30:00 - INFO - Order book features: Enabled
2024-01-15 10:30:05 - INFO - Fetched 8760 order book snapshots with features for SOLUSD
2024-01-15 10:30:10 - INFO - Loaded 8760 candles with order book features
2024-01-15 10:30:10 - INFO - Order book features included: ['ob_spread', 'ob_spread_pct', 'ob_mid_price', ...]
2024-01-15 10:30:10 - INFO - Final dataset shape: (8760, 45)
2024-01-15 10:30:10 - INFO - Training PPO model on 7008 data points
```
