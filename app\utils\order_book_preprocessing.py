import pandas as pd
from typing import Optional


def merge_partial_order_book_snapshots(df: pd.DataFrame, time_window_seconds: int = 10) -> pd.DataFrame:
    """
    Merge partial order book snapshots within a specified time window.

    Args:
        df (pd.DataFrame): DataFrame with columns ['timestamp', 'bids', 'asks'] where bids and asks are lists of dicts.
        time_window_seconds (int): Time window in seconds to merge snapshots.

    Returns:
        pd.DataFrame: DataFrame with merged snapshots, one per time window.
    """
    if df.empty:
        return df

    # Ensure timestamp is datetime and sorted
    df = df.copy()
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.sort_values('timestamp').reset_index(drop=True)

    merged_snapshots = []
    current_window_start = df['timestamp'].iloc[0]
    current_bids = []
    current_asks = []

    def merge_levels(levels1, levels2):
        # Merge two lists of price levels (dicts with 'price' and 'qty')
        # If price exists in both, sum qty, else keep unique
        merged = {}
        for level in levels1:
            merged[level['price']] = level['qty']
        for level in levels2:
            if level['price'] in merged:
                merged[level['price']] += level['qty']
            else:
                merged[level['price']] = level['qty']
        # Convert back to list of dicts
        return [{'price': price, 'qty': qty} for price, qty in merged.items()]

    for idx, row in df.iterrows():
        ts = row['timestamp']
        if (ts - current_window_start).total_seconds() <= time_window_seconds:
            # Merge bids and asks
            current_bids = merge_levels(current_bids, row['bids'] if row['bids'] else [])
            current_asks = merge_levels(current_asks, row['asks'] if row['asks'] else [])
        else:
            # Append the merged snapshot for the previous window
            merged_snapshots.append({
                'timestamp': current_window_start,
                'bids': current_bids,
                'asks': current_asks
            })
            # Reset for new window
            current_window_start = ts
            current_bids = row['bids'] if row['bids'] else []
            current_asks = row['asks'] if row['asks'] else []

    # Append the last window
    merged_snapshots.append({
        'timestamp': current_window_start,
        'bids': current_bids,
        'asks': current_asks
    })

    merged_df = pd.DataFrame(merged_snapshots)

    # Handle missing bid/ask sides gracefully by filling empty lists
    merged_df['bids'] = merged_df['bids'].apply(lambda x: x if isinstance(x, list) else [])
    merged_df['asks'] = merged_df['asks'].apply(lambda x: x if isinstance(x, list) else [])

    return merged_df
