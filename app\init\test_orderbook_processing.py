"""
Test script to validate order book processing and identify issues

This script helps debug:
1. Order book data structure issues
2. Spread calculation problems
3. Preprocessing data corruption
"""

import pandas as pd
import json
from datetime import datetime, timezone
from app.strategy.ppo_strategy import PPOStrategy
from app.utils.order_book_preprocessing import merge_partial_order_book_snapshots


def test_order_book_features():
    """Test order book feature calculation with sample data"""
    print("=== Testing Order Book Feature Calculation ===")
    
    # Create sample order book data
    sample_orderbook = {
        'pair': 'SOLUSD',
        'snapshot_time': datetime.now(timezone.utc),
        'bids': [
            {'price': 100.50, 'qty': 10.0},
            {'price': 100.49, 'qty': 5.0},
            {'price': 100.48, 'qty': 15.0}
        ],
        'asks': [
            {'price': 100.51, 'qty': 8.0},
            {'price': 100.52, 'qty': 12.0},
            {'price': 100.53, 'qty': 6.0}
        ],
        'checksum': 12345
    }
    
    strategy = PPOStrategy(pair='SOLUSD')
    features = strategy.calculate_orderbook_features(sample_orderbook)
    
    print("Sample order book:")
    print(f"  Best bid: {sample_orderbook['bids'][0]['price']}")
    print(f"  Best ask: {sample_orderbook['asks'][0]['price']}")
    
    print("\nCalculated features:")
    for key, value in features.items():
        print(f"  {key}: {value}")
    
    # Validate spread
    expected_spread = 100.51 - 100.50
    if abs(features['ob_spread'] - expected_spread) < 0.0001:
        print(f"✓ Spread calculation correct: {features['ob_spread']}")
    else:
        print(f"✗ Spread calculation error: expected {expected_spread}, got {features['ob_spread']}")


def test_crossed_market():
    """Test handling of crossed market (should not happen but test anyway)"""
    print("\n=== Testing Crossed Market Handling ===")
    
    # Create crossed market data (bid > ask)
    crossed_orderbook = {
        'pair': 'SOLUSD',
        'snapshot_time': datetime.now(timezone.utc),
        'bids': [
            {'price': 100.52, 'qty': 10.0},  # Bid higher than ask
            {'price': 100.51, 'qty': 5.0}
        ],
        'asks': [
            {'price': 100.50, 'qty': 8.0},   # Ask lower than bid
            {'price': 100.49, 'qty': 12.0}
        ],
        'checksum': 12345
    }
    
    strategy = PPOStrategy(pair='SOLUSD')
    features = strategy.calculate_orderbook_features(crossed_orderbook)
    
    print("Crossed market order book:")
    print(f"  Best bid: {crossed_orderbook['bids'][0]['price']}")
    print(f"  Best ask: {crossed_orderbook['asks'][0]['price']}")
    
    print("\nFeatures (should be defaults):")
    for key, value in features.items():
        print(f"  {key}: {value}")


def test_invalid_data():
    """Test handling of invalid order book data"""
    print("\n=== Testing Invalid Data Handling ===")
    
    test_cases = [
        # Empty bids
        {
            'pair': 'SOLUSD',
            'snapshot_time': datetime.now(timezone.utc),
            'bids': [],
            'asks': [{'price': 100.51, 'qty': 8.0}],
            'checksum': 12345
        },
        # Invalid price types
        {
            'pair': 'SOLUSD',
            'snapshot_time': datetime.now(timezone.utc),
            'bids': [{'price': 'invalid', 'qty': 10.0}],
            'asks': [{'price': 100.51, 'qty': 8.0}],
            'checksum': 12345
        },
        # Negative prices
        {
            'pair': 'SOLUSD',
            'snapshot_time': datetime.now(timezone.utc),
            'bids': [{'price': -100.50, 'qty': 10.0}],
            'asks': [{'price': 100.51, 'qty': 8.0}],
            'checksum': 12345
        }
    ]
    
    strategy = PPOStrategy(pair='SOLUSD')
    
    for i, test_case in enumerate(test_cases):
        print(f"\nTest case {i+1}:")
        features = strategy.calculate_orderbook_features(test_case)
        print(f"  Spread: {features['ob_spread']}")
        print(f"  All features default: {all(v == 0.0 for v in features.values())}")


def test_preprocessing():
    """Test the merge_partial_order_book_snapshots function"""
    print("\n=== Testing Order Book Preprocessing ===")
    
    # Create sample partial snapshots
    sample_data = [
        {
            'timestamp': datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            'bids': [{'price': 100.50, 'qty': 5.0}],
            'asks': [{'price': 100.51, 'qty': 3.0}]
        },
        {
            'timestamp': datetime(2024, 1, 1, 10, 0, 5, tzinfo=timezone.utc),  # 5 seconds later
            'bids': [{'price': 100.50, 'qty': 3.0}, {'price': 100.49, 'qty': 2.0}],
            'asks': [{'price': 100.51, 'qty': 2.0}]
        },
        {
            'timestamp': datetime(2024, 1, 1, 10, 0, 15, tzinfo=timezone.utc),  # 15 seconds later (new window)
            'bids': [{'price': 100.48, 'qty': 10.0}],
            'asks': [{'price': 100.52, 'qty': 8.0}]
        }
    ]
    
    df = pd.DataFrame(sample_data)
    print(f"Original snapshots: {len(df)}")
    
    # Test preprocessing
    merged_df = merge_partial_order_book_snapshots(df, time_window_seconds=10)
    print(f"After merging (10s window): {len(merged_df)}")
    
    for i, row in merged_df.iterrows():
        print(f"  Snapshot {i+1}:")
        print(f"    Timestamp: {row['timestamp']}")
        print(f"    Bids: {row['bids']}")
        print(f"    Asks: {row['asks']}")
        
        # Test feature calculation on merged data
        orderbook_data = {
            'pair': 'SOLUSD',
            'snapshot_time': row['timestamp'],
            'bids': row['bids'],
            'asks': row['asks'],
            'checksum': 0
        }
        
        strategy = PPOStrategy(pair='SOLUSD')
        features = strategy.calculate_orderbook_features(orderbook_data)
        print(f"    Spread: {features['ob_spread']}")


def main():
    """Run all tests"""
    print("Order Book Processing Test Suite")
    print("=" * 50)
    
    test_order_book_features()
    test_crossed_market()
    test_invalid_data()
    test_preprocessing()
    
    print("\n" + "=" * 50)
    print("Test suite completed!")


if __name__ == "__main__":
    main()
